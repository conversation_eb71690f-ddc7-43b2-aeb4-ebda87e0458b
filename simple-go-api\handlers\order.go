package handlers

import (
	"database/sql"
	"fmt"
	"net/http"
	"smarapp-api/database"
	"smarapp-api/models"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

type OrderHandler struct{}

func NewOrderHandler() *OrderHandler {
	return &OrderHandler{}
}

// CreateOrder godoc
// @Summary Create a new order (Buy a product)
// @Description Create a new order to purchase a product, automatically reduces stock
// @Tags Orders
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param order body models.CreateOrderRequest true "Order data"
// @Success 201 {object} models.OrderResponse
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /orders [post]
func (h *OrderHandler) CreateOrder(c *gin.Context) {
	var req models.CreateOrderRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		c.J<PERSON>N(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID, _ := c.Get("user_id")

	// Start transaction
	tx, err := database.DB.Begin()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to start transaction"})
		return
	}
	defer tx.Rollback()

	var orderTotal float64
	var products []models.Product
	var orderItems []models.OrderItem

	// Validate all products and calculate total
	for _, item := range req.Items {
		var product models.Product
		err = tx.QueryRow(
			"SELECT id, name, description, price, stock, created_by, created_at, updated_at FROM products WHERE id = ?",
			item.ProductID,
		).Scan(
			&product.ID, &product.Name, &product.Description, &product.Price,
			&product.Stock, &product.CreatedBy, &product.CreatedAt, &product.UpdatedAt,
		)

		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": fmt.Sprintf("Product %d not found", item.ProductID)})
			return
		}
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
			return
		}

		if product.Stock < item.Quantity {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Insufficient stock for product %s", product.Name)})
			return
		}

		itemTotal := product.Price * float64(item.Quantity)
		orderTotal += itemTotal

		orderItems = append(orderItems, models.OrderItem{
			ProductID: item.ProductID,
			Quantity:  item.Quantity,
			Price:     product.Price,
			Total:     itemTotal,
		})

		product.Stock -= item.Quantity
		products = append(products, product)
	}

	// Create order
	result, err := tx.Exec(
		"INSERT INTO orders (user_id, total, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?)",
		userID, orderTotal, models.OrderStatusPending, time.Now(), time.Now(),
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create order"})
		return
	}

	orderID, _ := result.LastInsertId()

	// Create order items and update stock
	for i, item := range orderItems {
		_, err = tx.Exec(
			"INSERT INTO order_items (order_id, product_id, quantity, price, total) VALUES (?, ?, ?, ?, ?)",
			orderID, item.ProductID, item.Quantity, item.Price, item.Total,
		)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create order item"})
			return
		}

		_, err = tx.Exec(
			"UPDATE products SET stock = stock - ?, updated_at = ? WHERE id = ?",
			item.Quantity, time.Now(), item.ProductID,
		)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update stock"})
			return
		}

		orderItems[i].OrderID = int(orderID)
	}

	// Complete order
	_, err = tx.Exec(
		"UPDATE orders SET status = ?, updated_at = ? WHERE id = ?",
		models.OrderStatusCompleted, time.Now(), orderID,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to complete order"})
		return
	}

	if err = tx.Commit(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
		return
	}

	order := models.Order{
		ID:        int(orderID),
		UserID:    userID.(int),
		Total:     orderTotal,
		Status:    models.OrderStatusCompleted,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Items:     orderItems,
	}

	c.JSON(http.StatusCreated, models.OrderResponse{
		Order:    order,
		Products: products,
	})
}

func (h *OrderHandler) GetUserOrders(c *gin.Context) {
	userID, _ := c.Get("user_id")

	rows, err := database.DB.Query(`
		SELECT o.id, o.user_id, o.total, o.status, o.created_at, o.updated_at
		FROM orders o
		WHERE o.user_id = ?
		ORDER BY o.created_at DESC
	`, userID)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch orders"})
		return
	}
	defer rows.Close()

	var orders []models.Order
	for rows.Next() {
		var order models.Order
		err := rows.Scan(
			&order.ID, &order.UserID, &order.Total, &order.Status, &order.CreatedAt, &order.UpdatedAt,
		)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to scan order"})
			return
		}

		// Get order items
		itemRows, err := database.DB.Query(`
			SELECT oi.id, oi.order_id, oi.product_id, oi.quantity, oi.price, oi.total
			FROM order_items oi
			WHERE oi.order_id = ?
		`, order.ID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch order items"})
			return
		}

		var items []models.OrderItem
		for itemRows.Next() {
			var item models.OrderItem
			err := itemRows.Scan(&item.ID, &item.OrderID, &item.ProductID, &item.Quantity, &item.Price, &item.Total)
			if err != nil {
				itemRows.Close()
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to scan order item"})
				return
			}
			items = append(items, item)
		}
		itemRows.Close()

		order.Items = items
		orders = append(orders, order)
	}

	c.JSON(http.StatusOK, orders)
}

func (h *OrderHandler) GetAllOrders(c *gin.Context) {
	rows, err := database.DB.Query(`
		SELECT o.id, o.user_id, o.total, o.status, o.created_at, o.updated_at
		FROM orders o
		ORDER BY o.created_at DESC
	`)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch orders"})
		return
	}
	defer rows.Close()

	var orders []models.Order
	for rows.Next() {
		var order models.Order
		err := rows.Scan(
			&order.ID, &order.UserID, &order.Total, &order.Status, &order.CreatedAt, &order.UpdatedAt,
		)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to scan order"})
			return
		}

		// Get order items
		itemRows, err := database.DB.Query(`
			SELECT oi.id, oi.order_id, oi.product_id, oi.quantity, oi.price, oi.total
			FROM order_items oi
			WHERE oi.order_id = ?
		`, order.ID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch order items"})
			return
		}

		var items []models.OrderItem
		for itemRows.Next() {
			var item models.OrderItem
			err := itemRows.Scan(&item.ID, &item.OrderID, &item.ProductID, &item.Quantity, &item.Price, &item.Total)
			if err != nil {
				itemRows.Close()
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to scan order item"})
				return
			}
			items = append(items, item)
		}
		itemRows.Close()

		order.Items = items
		orders = append(orders, order)
	}

	c.JSON(http.StatusOK, orders)
}

func (h *OrderHandler) GetOrder(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.Atoi(idParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	userID, _ := c.Get("user_id")
	role, _ := c.Get("role")

	query := `
		SELECT o.id, o.user_id, o.total, o.status, o.created_at, o.updated_at
		FROM orders o
		WHERE o.id = ?` + func() string {
			if role != models.RoleAdmin {
				return " AND o.user_id = ?"
			}
			return ""
		}() + `
	`

	args := []interface{}{id}
	if role != models.RoleAdmin {
		args = append(args, userID)
	}

	var order models.Order
	err = database.DB.QueryRow(query, args...).Scan(
		&order.ID, &order.UserID, &order.Total, &order.Status, &order.CreatedAt, &order.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		c.JSON(http.StatusNotFound, gin.H{"error": "Order not found"})
		return
	}
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}

	// Get order items
	itemRows, err := database.DB.Query(`
		SELECT oi.id, oi.order_id, oi.product_id, oi.quantity, oi.price, oi.total
		FROM order_items oi
		WHERE oi.order_id = ?
	`, order.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch order items"})
		return
	}
	defer itemRows.Close()

	var items []models.OrderItem
	for itemRows.Next() {
		var item models.OrderItem
		err := itemRows.Scan(&item.ID, &item.OrderID, &item.ProductID, &item.Quantity, &item.Price, &item.Total)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to scan order item"})
			return
		}
		items = append(items, item)
	}

	order.Items = items
	c.JSON(http.StatusOK, order)
}
